/* Flag Icons CSS - مكتبة أيقونات الأعلام */

.flag-icon {
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    position: relative;
    display: inline-block;
    width: 1.33333333em;
    line-height: 1em;
    margin-right: 8px;
    vertical-align: middle;
}

.flag-icon:before {
    content: "\00a0";
}

/* العلم المصري للعربية */
.flag-icon-eg {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cpath fill='%23ce1126' d='M0 0h900v200H0z'/%3E%3Cpath fill='%23fff' d='M0 200h900v200H0z'/%3E%3Cpath fill='%23000' d='M0 400h900v200H0z'/%3E%3Cg fill='%23ffd700'%3E%3Ccircle cx='450' cy='300' r='60'/%3E%3Cpath d='M450 240c33.137 0 60 26.863 60 60s-26.863 60-60 60-60-26.863-60-60 26.863-60 60-60zm0 20c-22.091 0-40 17.909-40 40s17.909 40 40 40 40-17.909 40-40-17.909-40-40-40z'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم البريطاني للإنجليزية */
.flag-icon-gb {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 30'%3E%3Cclippath id='a'%3E%3Cpath d='M0 0v30h60V0z'/%3E%3C/clippath%3E%3Cclippath id='b'%3E%3Cpath d='M30 15h30v15zv15H0zH0V0zV0h30z'/%3E%3C/clippath%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='M0 0v30h60V0z' fill='%23012169'/%3E%3Cpath d='M0 0l60 30m0-30L0 30' stroke='%23fff' stroke-width='6'/%3E%3Cpath d='M0 0l60 30m0-30L0 30' clip-path='url(%23b)' stroke='%23C8102E' stroke-width='4'/%3E%3Cpath d='M30 0v30M0 15h60' stroke='%23fff' stroke-width='10'/%3E%3Cpath d='M30 0v30M0 15h60' stroke='%23C8102E' stroke-width='6'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم الأمريكي */
.flag-icon-us {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 7410 3900'%3E%3Cpath fill='%23b22234' d='M0 0h7410v3900H0z'/%3E%3Cpath d='M0 450h7410m0 600H0m0 600h7410m0 600H0m0 600h7410m0 600H0' stroke='%23fff' stroke-width='300'/%3E%3Cpath fill='%233c3b6e' d='M0 0h2964v2100H0z'/%3E%3C/svg%3E");
}

/* العلم الفرنسي */
.flag-icon-fr {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cpath fill='%23ed2939' d='M600 0h300v600H600z'/%3E%3Cpath fill='%23fff' d='M300 0h300v600H300z'/%3E%3Cpath fill='%23002395' d='M0 0h300v600H0z'/%3E%3C/svg%3E");
}

/* العلم الألماني */
.flag-icon-de {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 5 3'%3E%3Cpath d='M0 0h5v1H0z'/%3E%3Cpath fill='%23D00' d='M0 1h5v1H0z'/%3E%3Cpath fill='%23FFCE00' d='M0 2h5v1H0z'/%3E%3C/svg%3E");
}

/* العلم الإسباني */
.flag-icon-es {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 750 500'%3E%3Cpath fill='%23AA151B' d='M0 0h750v125H0z'/%3E%3Cpath fill='%23F1BF00' d='M0 125h750v250H0z'/%3E%3Cpath fill='%23AA151B' d='M0 375h750v125H0z'/%3E%3C/svg%3E");
}

/* العلم الإيطالي */
.flag-icon-it {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 3 2'%3E%3Cpath fill='%23009246' d='M0 0h1v2H0z'/%3E%3Cpath fill='%23fff' d='M1 0h1v2H1z'/%3E%3Cpath fill='%23ce2b37' d='M2 0h1v2H2z'/%3E%3C/svg%3E");
}

/* العلم الروسي */
.flag-icon-ru {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 6'%3E%3Cpath fill='%23fff' d='M0 0h9v2H0z'/%3E%3Cpath fill='%230039a6' d='M0 2h9v2H0z'/%3E%3Cpath fill='%23d52b1e' d='M0 4h9v2H0z'/%3E%3C/svg%3E");
}

/* العلم الصيني */
.flag-icon-cn {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 20'%3E%3Cpath fill='%23de2910' d='M0 0h30v20H0z'/%3E%3Cpath fill='%23ffde00' d='M5 3l.8 2.5h2.6L6.6 7l.8 2.5L5 8l-2.4 1.5L3.4 7 1.6 5.5h2.6z'/%3E%3C/svg%3E");
}

/* العلم الياباني */
.flag-icon-jp {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cpath fill='%23fff' d='M0 0h900v600H0z'/%3E%3Ccircle fill='%23bc002d' cx='450' cy='300' r='180'/%3E%3C/svg%3E");
}

/* العلم الكوري الجنوبي */
.flag-icon-kr {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cpath fill='%23fff' d='M0 0h900v600H0z'/%3E%3Ccircle fill='%23c60c30' cx='450' cy='300' r='120'/%3E%3Cpath fill='%23003478' d='M450 180a120 120 0 0 0 0 240 60 60 0 0 1 0-120 60 60 0 0 0 0-120z'/%3E%3C/svg%3E");
}

/* العلم البرازيلي */
.flag-icon-br {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 720 504'%3E%3Cpath fill='%23009739' d='M0 0h720v504H0z'/%3E%3Cpath fill='%23fedd00' d='M360 12.6L692.3 252 360 491.4 27.7 252z'/%3E%3Ccircle fill='%23012169' cx='360' cy='252' r='108'/%3E%3C/svg%3E");
}

/* العلم الهندي */
.flag-icon-in {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 225 150'%3E%3Cpath fill='%23ff9933' d='M0 0h225v50H0z'/%3E%3Cpath fill='%23fff' d='M0 50h225v50H0z'/%3E%3Cpath fill='%23138808' d='M0 100h225v50H0z'/%3E%3Ccircle fill='none' stroke='%23000080' stroke-width='1.5' cx='112.5' cy='75' r='20'/%3E%3C/svg%3E");
}

/* العلم التركي */
.flag-icon-tr {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 800'%3E%3Cpath fill='%23e30a17' d='M0 0h1200v800H0z'/%3E%3Ccircle fill='%23fff' cx='425' cy='400' r='120'/%3E%3Ccircle fill='%23e30a17' cx='475' cy='400' r='96'/%3E%3Cpath fill='%23fff' d='M580 400l-20-15 12-19-23 7-12-19-12 19-23-7 12 19-20 15 20 15-12 19 23-7 12 19 12-19 23 7-12-19z'/%3E%3C/svg%3E");
}

/* تنسيقات إضافية للأعلام */
.flag-icon-small {
    width: 1em;
    height: 0.75em;
}

.flag-icon-medium {
    width: 1.5em;
    height: 1.125em;
}

.flag-icon-large {
    width: 2em;
    height: 1.5em;
}

/* تنسيقات خاصة لقائمة اللغات */
.language-selector .flag-icon {
    width: 20px;
    height: 15px;
    margin-right: 8px;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.language-selector .flag-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* تنسيقات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .flag-icon {
        filter: brightness(0.9);
    }
    
    .flag-icon:hover {
        filter: brightness(1.1);
    }
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .language-selector .flag-icon {
        width: 18px;
        height: 13.5px;
        margin-right: 6px;
    }
}

/* تأثيرات حركية للأعلام */
@keyframes flagWave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    75% { transform: rotate(-1deg); }
}

.flag-icon-animated {
    animation: flagWave 2s ease-in-out infinite;
}

/* تنسيقات خاصة للتنقل */
.nav-area .flag-icon {
    margin-right: 6px;
    margin-left: 0;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    display: inline-block;
    position: relative;
    top: -1px;
}

.nav-area .submenu .flag-icon {
    margin-right: 8px;
    width: 18px;
    height: 13px;
}

/* تحسينات الأداء */
.flag-icon {
    will-change: transform;
    backface-visibility: hidden;
}

/* دعم RTL */
[dir="rtl"] .flag-icon {
    margin-right: 0;
    margin-left: 8px;
}

[dir="rtl"] .nav-area .flag-icon {
    margin-right: 0;
    margin-left: 6px;
}

[dir="rtl"] .nav-area .submenu .flag-icon {
    margin-right: 0;
    margin-left: 8px;
}

/* تنسيقات خاصة للاتجاه الإنجليزي LTR */
[dir="ltr"] .nav-area .flag-icon {
    margin-right: 6px;
    margin-left: 0;
    vertical-align: baseline;
    display: inline-block;
    position: relative;
    top: -1px;
}
